-- 创建车型隔声量和混响时间数据表

-- 车型隔声量数据表
CREATE TABLE vehicle_sound_insulation_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_model_id INT NOT NULL COMMENT '车型ID',
    
    -- 中心频率数据 (200Hz-10000Hz)
    freq_200 DECIMAL(5,2) COMMENT '200Hz隔声量(dB)',
    freq_250 DECIMAL(5,2) COMMENT '250Hz隔声量(dB)',
    freq_315 DECIMAL(5,2) COMMENT '315Hz隔声量(dB)',
    freq_400 DECIMAL(5,2) COMMENT '400Hz隔声量(dB)',
    freq_500 DECIMAL(5,2) COMMENT '500Hz隔声量(dB)',
    freq_630 DECIMAL(5,2) COMMENT '630Hz隔声量(dB)',
    freq_800 DECIMAL(5,2) COMMENT '800Hz隔声量(dB)',
    freq_1000 DECIMAL(5,2) COMMENT '1000Hz隔声量(dB)',
    freq_1250 DECIMAL(5,2) COMMENT '1250Hz隔声量(dB)',
    freq_1600 DECIMAL(5,2) COMMENT '1600Hz隔声量(dB)',
    freq_2000 DECIMAL(5,2) COMMENT '2000Hz隔声量(dB)',
    freq_2500 DECIMAL(5,2) COMMENT '2500Hz隔声量(dB)',
    freq_3150 DECIMAL(5,2) COMMENT '3150Hz隔声量(dB)',
    freq_4000 DECIMAL(5,2) COMMENT '4000Hz隔声量(dB)',
    freq_5000 DECIMAL(5,2) COMMENT '5000Hz隔声量(dB)',
    freq_6300 DECIMAL(5,2) COMMENT '6300Hz隔声量(dB)',
    freq_8000 DECIMAL(5,2) COMMENT '8000Hz隔声量(dB)',
    freq_10000 DECIMAL(5,2) COMMENT '10000Hz隔声量(dB)',
    
    -- 测试信息
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    remarks TEXT COMMENT '备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_models(id),
    UNIQUE KEY unique_vehicle_insulation (vehicle_model_id)
);

-- 车型混响时间数据表
CREATE TABLE vehicle_reverberation_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_model_id INT NOT NULL COMMENT '车型ID',
    
    -- 中心频率数据 (200Hz-10000Hz)
    freq_200 DECIMAL(5,3) COMMENT '200Hz混响时间(s)',
    freq_250 DECIMAL(5,3) COMMENT '250Hz混响时间(s)',
    freq_315 DECIMAL(5,3) COMMENT '315Hz混响时间(s)',
    freq_400 DECIMAL(5,3) COMMENT '400Hz混响时间(s)',
    freq_500 DECIMAL(5,3) COMMENT '500Hz混响时间(s)',
    freq_630 DECIMAL(5,3) COMMENT '630Hz混响时间(s)',
    freq_800 DECIMAL(5,3) COMMENT '800Hz混响时间(s)',
    freq_1000 DECIMAL(5,3) COMMENT '1000Hz混响时间(s)',
    freq_1250 DECIMAL(5,3) COMMENT '1250Hz混响时间(s)',
    freq_1600 DECIMAL(5,3) COMMENT '1600Hz混响时间(s)',
    freq_2000 DECIMAL(5,3) COMMENT '2000Hz混响时间(s)',
    freq_2500 DECIMAL(5,3) COMMENT '2500Hz混响时间(s)',
    freq_3150 DECIMAL(5,3) COMMENT '3150Hz混响时间(s)',
    freq_4000 DECIMAL(5,3) COMMENT '4000Hz混响时间(s)',
    freq_5000 DECIMAL(5,3) COMMENT '5000Hz混响时间(s)',
    freq_6300 DECIMAL(5,3) COMMENT '6300Hz混响时间(s)',
    freq_8000 DECIMAL(5,3) COMMENT '8000Hz混响时间(s)',
    freq_10000 DECIMAL(5,3) COMMENT '10000Hz混响时间(s)',
    
    -- 测试信息
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    remarks TEXT COMMENT '备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_models(id),
    UNIQUE KEY unique_vehicle_reverberation (vehicle_model_id)
);

-- 插入测试数据（车型隔声量）
INSERT INTO vehicle_sound_insulation_data (
    vehicle_model_id, 
    freq_200, freq_250, freq_315, freq_400, freq_500, freq_630, freq_800, freq_1000, 
    freq_1250, freq_1600, freq_2000, freq_2500, freq_3150, freq_4000, freq_5000, 
    freq_6300, freq_8000, freq_10000,
    test_date, test_engineer, test_location, remarks
) VALUES 
(1, 25.5, 28.2, 31.8, 35.4, 38.9, 42.1, 45.3, 48.7, 51.2, 53.8, 56.4, 58.9, 61.3, 63.7, 65.8, 67.5, 69.1, 70.2, '2024-01-15', '张工程师', '上汽通用五菱试验中心', '宝骏530车型隔声量测试'),
(2, 23.8, 26.9, 30.2, 33.7, 37.1, 40.5, 43.8, 47.2, 49.8, 52.3, 54.9, 57.4, 59.8, 62.1, 64.2, 66.0, 67.6, 68.9, '2024-01-16', '李工程师', '上汽通用五菱试验中心', '宝骏510车型隔声量测试'),
(3, 22.1, 25.3, 28.7, 32.1, 35.6, 38.9, 42.1, 45.5, 48.0, 50.6, 53.1, 55.7, 58.2, 60.6, 62.8, 64.7, 66.3, 67.6, '2024-01-17', '王工程师', '上汽通用五菱试验中心', '五菱宏光MINI EV车型隔声量测试');

-- 插入测试数据（车型混响时间）
INSERT INTO vehicle_reverberation_data (
    vehicle_model_id, 
    freq_200, freq_250, freq_315, freq_400, freq_500, freq_630, freq_800, freq_1000, 
    freq_1250, freq_1600, freq_2000, freq_2500, freq_3150, freq_4000, freq_5000, 
    freq_6300, freq_8000, freq_10000,
    test_date, test_engineer, test_location, remarks
) VALUES 
(1, 0.85, 0.78, 0.72, 0.68, 0.65, 0.62, 0.59, 0.56, 0.54, 0.52, 0.50, 0.48, 0.46, 0.44, 0.42, 0.40, 0.38, 0.36, '2024-01-15', '张工程师', '上汽通用五菱试验中心', '宝骏530车型混响时间测试'),
(2, 0.92, 0.84, 0.77, 0.72, 0.68, 0.64, 0.61, 0.58, 0.55, 0.53, 0.51, 0.49, 0.47, 0.45, 0.43, 0.41, 0.39, 0.37, '2024-01-16', '李工程师', '上汽通用五菱试验中心', '宝骏510车型混响时间测试'),
(3, 1.05, 0.96, 0.88, 0.81, 0.75, 0.70, 0.66, 0.62, 0.59, 0.56, 0.54, 0.52, 0.50, 0.48, 0.46, 0.44, 0.42, 0.40, '2024-01-17', '王工程师', '上汽通用五菱试验中心', '五菱宏光MINI EV车型混响时间测试');
