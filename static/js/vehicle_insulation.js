// 车型隔声量对比功能JavaScript

class VehicleInsulationManager {
    constructor() {
        this.selectedVehicles = new Set();
        this.vehicleData = [];
        this.currentComparisonData = null;
        this.chart = null;
        
        this.init();
    }
    
    init() {
        this.loadVehicles();
        this.bindEvents();
        this.initMultiselect();
    }
    
    async loadVehicles() {
        try {
            const response = await request.get('/sound_insulation/api/vehicle_insulation/vehicles');
            this.vehicleData = response.data;
            this.populateVehicleOptions();
        } catch (error) {
            showMessage('加载车型列表失败: ' + error.message, 'error');
        }
    }
    
    populateVehicleOptions() {
        const optionsContainer = document.getElementById('multiselect-options');
        optionsContainer.innerHTML = '';
        
        this.vehicleData.forEach(vehicle => {
            const option = document.createElement('div');
            option.className = 'multiselect-option';
            option.dataset.value = vehicle.id;
            option.innerHTML = `
                <input type="checkbox" id="vehicle_${vehicle.id}" value="${vehicle.id}">
                <label for="vehicle_${vehicle.id}">${vehicle.name} (${vehicle.code})</label>
            `;
            optionsContainer.appendChild(option);
        });
    }
    
    initMultiselect() {
        const display = document.getElementById('multiselect-display');
        const dropdown = document.getElementById('multiselect-dropdown');
        const searchInput = document.getElementById('vehicle-search');
        
        // 点击显示区域切换下拉框
        display.addEventListener('click', () => {
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        });
        
        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.multiselect-container')) {
                dropdown.style.display = 'none';
            }
        });
        
        // 搜索功能
        searchInput.addEventListener('input', (e) => {
            this.filterVehicleOptions(e.target.value);
        });
        
        // 选项变化事件
        dropdown.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox') {
                this.handleVehicleSelection(e.target);
            }
        });
    }
    
    filterVehicleOptions(searchTerm) {
        const options = document.querySelectorAll('.multiselect-option');
        const term = searchTerm.toLowerCase();
        
        options.forEach(option => {
            const label = option.querySelector('label').textContent.toLowerCase();
            option.style.display = label.includes(term) ? 'block' : 'none';
        });
    }
    
    handleVehicleSelection(checkbox) {
        const vehicleId = parseInt(checkbox.value);
        
        if (checkbox.checked) {
            this.selectedVehicles.add(vehicleId);
        } else {
            this.selectedVehicles.delete(vehicleId);
        }
        
        this.updateMultiselectDisplay();
    }
    
    updateMultiselectDisplay() {
        const display = document.getElementById('multiselect-display');
        const placeholder = display.querySelector('.placeholder');
        
        if (this.selectedVehicles.size === 0) {
            placeholder.textContent = '请选择车型（可多选）';
            placeholder.style.display = 'block';
            return;
        }
        
        placeholder.style.display = 'none';
        
        // 清除现有的选中项显示
        const existingTags = display.querySelectorAll('.selected-tag');
        existingTags.forEach(tag => tag.remove());
        
        // 添加选中项标签
        this.selectedVehicles.forEach(vehicleId => {
            const vehicle = this.vehicleData.find(v => v.id === vehicleId);
            if (vehicle) {
                const tag = document.createElement('span');
                tag.className = 'selected-tag';
                tag.innerHTML = `
                    ${vehicle.name}
                    <i class="fas fa-times" data-vehicle-id="${vehicleId}"></i>
                `;
                display.appendChild(tag);
            }
        });
        
        // 绑定删除标签事件
        display.querySelectorAll('.selected-tag i').forEach(icon => {
            icon.addEventListener('click', (e) => {
                e.stopPropagation();
                const vehicleId = parseInt(e.target.dataset.vehicleId);
                this.removeVehicleSelection(vehicleId);
            });
        });
    }
    
    removeVehicleSelection(vehicleId) {
        this.selectedVehicles.delete(vehicleId);
        
        // 取消复选框选中状态
        const checkbox = document.getElementById(`vehicle_${vehicleId}`);
        if (checkbox) {
            checkbox.checked = false;
        }
        
        this.updateMultiselectDisplay();
    }
    
    bindEvents() {
        // 生成对比表按钮
        document.getElementById('generate-btn').addEventListener('click', () => {
            this.generateComparison();
        });
        
        // 导出数据按钮
        document.getElementById('export-btn').addEventListener('click', () => {
            this.exportData();
        });
    }
    
    async generateComparison() {
        if (this.selectedVehicles.size === 0) {
            showMessage('请至少选择一个车型', 'warning');
            return;
        }
        
        this.showLoading(true);
        
        try {
            const response = await request.post('/sound_insulation/api/vehicle_insulation/comparison', {
                vehicle_ids: Array.from(this.selectedVehicles)
            });
            
            this.currentComparisonData = response.data;
            this.displayComparisonResults();
            showMessage('对比数据生成成功', 'success');
        } catch (error) {
            showMessage('生成对比数据失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    displayComparisonResults() {
        const data = this.currentComparisonData;
        
        // 显示结果区域，隐藏空状态
        document.getElementById('results-card').style.display = 'block';
        document.getElementById('empty-state').style.display = 'none';
        
        // 更新车型数量
        document.getElementById('vehicle-count').textContent = `${data.vehicle_info.length} 个车型`;
        
        // 生成数据表格
        this.generateTable(data);
        
        // 生成图表
        this.generateChart(data);
        
        // 生成测试信息表
        this.generateTestInfoTable(data);
    }
    
    generateTable(data) {
        const table = document.getElementById('comparison-table');
        const thead = table.querySelector('thead');
        const tbody = table.querySelector('tbody');
        
        // 生成表头
        let headerHtml = '<tr><th class="frequency-column">中心频率(Hz)</th>';
        data.vehicle_info.forEach(vehicle => {
            headerHtml += `<th>${vehicle.name}<br><small class="text-muted">(dB)</small></th>`;
        });
        headerHtml += '</tr>';
        thead.innerHTML = headerHtml;
        
        // 生成表格数据
        let bodyHtml = '';
        data.table_data.forEach(row => {
            bodyHtml += `<tr><td class="frequency-column">${row.frequency}</td>`;
            data.vehicle_info.forEach(vehicle => {
                const value = row[`vehicle_${vehicle.id}`];
                const displayValue = value !== null && value !== undefined ? value.toFixed(2) : '-';
                bodyHtml += `<td>${displayValue}</td>`;
            });
            bodyHtml += '</tr>';
        });
        tbody.innerHTML = bodyHtml;
    }
    
    generateChart(data) {
        const chartContainer = document.getElementById('chart-container');
        
        if (this.chart) {
            this.chart.dispose();
        }
        
        this.chart = echarts.init(chartContainer);
        
        const option = {
            title: {
                text: '车型隔声量对比图',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    let result = `频率: ${params[0].axisValue}Hz<br/>`;
                    params.forEach(param => {
                        if (param.value !== null) {
                            result += `${param.seriesName}: ${param.value.toFixed(2)} dB<br/>`;
                        }
                    });
                    return result;
                }
            },
            legend: {
                data: data.chart_data.series.map(s => s.name),
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.chart_data.frequencies,
                name: '频率(Hz)',
                nameLocation: 'middle',
                nameGap: 30
            },
            yAxis: {
                type: 'value',
                name: '隔声量(dB)',
                nameLocation: 'middle',
                nameGap: 50
            },
            series: data.chart_data.series.map((seriesData, index) => ({
                name: seriesData.name,
                type: 'line',
                data: seriesData.data,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 2
                },
                itemStyle: {
                    color: this.getChartColor(index)
                }
            }))
        };
        
        this.chart.setOption(option);
        
        // 绑定点击事件
        this.chart.on('click', (params) => {
            if (params.componentType === 'series') {
                const vehicleId = data.chart_data.series[params.seriesIndex].vehicle_id;
                this.showTestImage(vehicleId);
            }
        });
        
        // 响应式调整
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });
    }
    
    getChartColor(index) {
        const colors = [
            '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
            '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#5470c6'
        ];
        return colors[index % colors.length];
    }
    
    generateTestInfoTable(data) {
        const tbody = document.querySelector('#test-info-table tbody');
        let html = '';
        
        data.vehicle_info.forEach(vehicle => {
            html += `
                <tr>
                    <td>${vehicle.name}</td>
                    <td>${vehicle.test_date || '-'}</td>
                    <td>${vehicle.test_engineer || '-'}</td>
                    <td>${vehicle.test_location || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="vehicleInsulationManager.showTestImage(${vehicle.id})">
                            <i class="fas fa-image me-1"></i>查看附图
                        </button>
                    </td>
                </tr>
            `;
        });
        
        tbody.innerHTML = html;
    }
    
    async showTestImage(vehicleId) {
        try {
            const response = await request.get(`/sound_insulation/api/vehicle_insulation/test_image?vehicle_id=${vehicleId}`);
            const imageInfo = response.data;
            
            // 更新模态框内容
            document.getElementById('modal-vehicle-name').textContent = imageInfo.vehicle_name || '-';
            document.getElementById('modal-test-date').textContent = imageInfo.test_date || '-';
            document.getElementById('modal-test-engineer').textContent = imageInfo.test_engineer || '-';
            document.getElementById('modal-test-location').textContent = imageInfo.test_location || '-';
            document.querySelector('#modal-remarks p').textContent = imageInfo.remarks || '-';
            
            const testImage = document.getElementById('test-image');
            const noImage = document.getElementById('no-image');
            
            if (imageInfo.test_image_path) {
                testImage.src = `/static/uploads/${imageInfo.test_image_path}`;
                testImage.style.display = 'block';
                noImage.style.display = 'none';
            } else {
                testImage.style.display = 'none';
                noImage.style.display = 'block';
            }
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            modal.show();
            
        } catch (error) {
            showMessage('获取测试图片失败: ' + error.message, 'error');
        }
    }
    
    async exportData() {
        if (!this.currentComparisonData) {
            showMessage('请先生成对比数据', 'warning');
            return;
        }
        
        try {
            const response = await fetch('/sound_insulation/api/vehicle_insulation/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vehicle_ids: Array.from(this.selectedVehicles)
                })
            });
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'vehicle_insulation_comparison.csv';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                showMessage('数据导出成功', 'success');
            } else {
                throw new Error('导出失败');
            }
        } catch (error) {
            showMessage('导出数据失败: ' + error.message, 'error');
        }
    }
    
    showLoading(show) {
        const loading = document.getElementById('loading-indicator');
        const results = document.getElementById('results-card');
        const empty = document.getElementById('empty-state');
        
        if (show) {
            loading.style.display = 'block';
            results.style.display = 'none';
            empty.style.display = 'none';
        } else {
            loading.style.display = 'none';
        }
    }
}
