{% extends "base.html" %}

{% block title %}车型混响时间对比 - NVH数据管理系统{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/sound_insulation.css') }}" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">车型混响时间对比</h1>
</div>

<!-- 查询条件 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>查询条件
        </h5>
    </div>
    <div class="card-body">
        <form id="query-form">
            <div class="row">
                <div class="col-md-8">
                    <label for="vehicle-multiselect" class="form-label">选择车型</label>
                    <div class="multiselect-container">
                        <div class="multiselect-display" id="multiselect-display">
                            <span class="placeholder">请选择车型（可多选）</span>
                        </div>
                        <div class="multiselect-dropdown" id="multiselect-dropdown">
                            <div class="multiselect-search">
                                <input type="text" class="form-control form-control-sm" placeholder="搜索车型..." id="vehicle-search">
                            </div>
                            <div class="multiselect-options" id="multiselect-options">
                                <!-- 动态生成选项 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="button" class="btn btn-primary" id="generate-btn">
                        <i class="fas fa-chart-line me-1"></i>生成对比表
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card" id="results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>车型混响时间对比结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <span id="vehicle-count" class="badge bg-secondary">0 个车型</span>
            <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                <i class="fas fa-download me-1"></i>导出数据
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- 数据表格 -->
        <div class="table-responsive mb-4">
            <table class="table table-striped table-hover" id="comparison-table">
                <thead class="table-dark">
                    <!-- 动态生成表头 -->
                </thead>
                <tbody>
                    <!-- 动态生成表格内容 -->
                </tbody>
            </table>
        </div>
        
        <!-- 折线对比图 -->
        <div class="mb-4">
            <h6><i class="fas fa-chart-line me-2"></i>折线对比图</h6>
            <div id="chart-container" style="width: 100%; height: 400px;"></div>
        </div>
        
        <!-- 测试信息 -->
        <div class="mt-4">
            <h6><i class="fas fa-info-circle me-2"></i>测试信息</h6>
            <div class="table-responsive">
                <table class="table table-sm table-bordered" id="test-info-table">
                    <thead class="table-light">
                        <tr>
                            <th>车型</th>
                            <th>测试日期</th>
                            <th>测试工程师</th>
                            <th>测试地点</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态生成测试信息 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 空状态提示 -->
<div class="card" id="empty-state" style="display: block;">
    <div class="card-body text-center py-5">
        <i class="fas fa-wave-square fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">请选择车型生成对比表</h5>
        <p class="text-muted">选择一个或多个车型，点击"生成对比表"按钮查看混响时间对比数据</p>
    </div>
</div>

<!-- 加载提示 -->
<div class="text-center py-4" id="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2 text-muted">正在生成对比数据...</p>
</div>

<!-- 测试图片模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-image me-2"></i>测试图片
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="text-center">
                            <img id="test-image" src="" alt="测试图片" class="img-fluid" style="max-height: 400px;">
                            <div id="no-image" class="text-muted py-5" style="display: none;">
                                <i class="fas fa-image fa-3x mb-3"></i>
                                <p>暂无测试图片</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>测试信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>车型:</strong></td>
                                <td id="modal-vehicle-name">-</td>
                            </tr>
                            <tr>
                                <td><strong>测试日期:</strong></td>
                                <td id="modal-test-date">-</td>
                            </tr>
                            <tr>
                                <td><strong>测试工程师:</strong></td>
                                <td id="modal-test-engineer">-</td>
                            </tr>
                            <tr>
                                <td><strong>测试地点:</strong></td>
                                <td id="modal-test-location">-</td>
                            </tr>
                        </table>
                        <div id="modal-remarks" class="mt-3">
                            <h6>备注</h6>
                            <p class="text-muted small">-</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/vehicle_reverberation.js') }}"></script>
<script>
    // 初始化车型混响时间管理器
    document.addEventListener('DOMContentLoaded', function() {
        window.vehicleReverberationManager = new VehicleReverberationManager();
    });
</script>
{% endblock %}
